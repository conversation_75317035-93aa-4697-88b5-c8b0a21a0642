import 'package:easydine_main/models/menuItem.dart';
import 'package:easydine_main/services/menu_service.dart';
import 'package:easydine_main/widgets/quantity_controls.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../blocs/demo_customization/customization_modal.dart';
import '../../../blocs/demopos/pos_bloc.dart';
import '../../../blocs/demopos/pos_event.dart';
import '../../../models/cartItem.dart';

class DemoCartItemTile extends StatefulWidget {
  final CartItem item;

  const DemoCartItemTile({
    super.key,
    required this.item,
  });

  @override
  State<DemoCartItemTile> createState() => _DemoCartItemTileState();
}

class _DemoCartItemTileState extends State<DemoCartItemTile> {
  bool _isCustomizationsExpanded = false;

  void _showCustomizationModal(BuildContext context, MenuItem menuItem) {
    showModalBottomSheet(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => DemoItemCustomizationBottomSheet(item: menuItem),
    );
  }

  void _duplicateItem(BuildContext context) {
    final customization = widget.item.customization;
    if (customization?['type'] == 'miscellaneous') {
      // For miscellaneous items, use the original item ID
      final dishId = widget.item.baseItemId ?? widget.item.id;
      context.read<DemoPOSBloc>().add(
            AddToCart(
              MenuItem(
                id: dishId,
                name: widget.item.name,
                price: widget.item.price,
                image: '',
                category: 'Miscellaneous',
                description: '',
                ingredients: [],
                prepTime: 0,
                rating: 0,
                isSpicy: false,
                dietaryInfo: [],
              ),
              id: dishId,
              name: widget.item.name,
              price: widget.item.price,
              customization: customization,
            ),
          );
    } else {
      // For regular items, get the dish ID (food ID) not the cart item ID
      final baseItemId = customization?['type'] == 'customized'
          ? customization!['baseItemId']
          : (widget.item.baseItemId ?? widget.item.id);

      final menuItem = MenuService.getAllMenuItems().firstWhere(
        (item) => item.id == baseItemId,
        orElse: () => MenuItem(
          id: baseItemId,
          name: widget.item.name,
          price: widget.item.price,
          image: '',
          category: 'Customized',
          description: '',
          ingredients: [],
          prepTime: 0,
          rating: 0,
          isSpicy: false,
          dietaryInfo: [],
        ),
      );

      context.read<DemoPOSBloc>().add(
            AddToCart(
              menuItem,
              id: menuItem.id,
              name: menuItem.name,
              price: widget.item.price,
              customization: customization,
            ),
          );
    }
  }

  double get _basePrice {
    // Return only the base price without any side dish price
    return widget.item.price -
        (widget.item.customization?['side']?['price'] ?? 0.0);
  }

  String _getCustomizationSummary() {
    if (widget.item.customization == null ||
        widget.item.customization!.isEmpty) {
      return '';
    }

    final List<String> summary = [];

    if (widget.item.customization!['cookingOption'] != null) {
      summary.add(widget.item.customization!['cookingOption']);
    }

    if (widget.item.customization!['side'] != null) {
      summary.add(widget.item.customization!['side']['name']);
    }

    return summary.isNotEmpty ? ' (${summary.join(', ')})' : '';
  }

  @override
  Widget build(BuildContext context) {
    final isCustomized = widget.item.customization?['type'] == 'customized';
    final isMiscellaneous =
        widget.item.customization?['type'] == 'miscellaneous';

    // Create a default MenuItem for miscellaneous or customized items
    final defaultMenuItem = MenuItem(
      id: widget.item.id,
      name: widget.item.name,
      price: widget.item.price,
      image: '',
      category: isCustomized ? 'Customized' : 'Miscellaneous',
      description: '',
      ingredients: [],
      prepTime: 0,
      rating: 0,
      isSpicy: false,
      dietaryInfo: [],
    );

    // Try to find the menu item, use default if not found
    final MenuItem menuItem = isMiscellaneous
        ? defaultMenuItem
        : MenuService.getAllMenuItems().firstWhere(
            (item) =>
                item.id ==
                (isCustomized
                    ? widget.item.customization != null
                        ? widget.item.customization!['baseItemId']
                        : null
                    : widget.item.id),
            orElse: () => defaultMenuItem,
          );

    // Build customization summary
    final List<String> customizationDetails = [];
    if (isCustomized) {
      final customization = widget.item.customization!;

      if (customization['cookingOption'] != null) {
        customizationDetails.add(customization['cookingOption']);
      }

      if (customization['side'] != null) {
        customizationDetails.add('Side: ${customization['side']['name']}');
      }

      if (customization['allergies'] != null &&
          (customization['allergies'] as List).isNotEmpty) {
        customizationDetails
            .add('Allergies: ${customization['allergies'].join(', ')}');
      }

      if (customization['notes']?.isNotEmpty ?? false) {
        customizationDetails.add('Notes: ${customization['notes']}');
      }
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        return ClipRRect(
          child: Dismissible(
            key: Key(widget.item.id),
            background: Container(
              color: Colors.green.withOpacity(0.7),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Icon(Icons.copy, color: Colors.white),
                  ),
                  Text(
                    'Duplicate',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            secondaryBackground: Container(
              color: Colors.red.withOpacity(0.7),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16.0),
                    child: Icon(Icons.edit, color: Colors.white),
                  ),
                  Text(
                    'Edit Customization',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            movementDuration: const Duration(milliseconds: 200),
            dismissThresholds: const {
              DismissDirection.startToEnd: 0.3,
              DismissDirection.endToStart: 0.3,
            },
            confirmDismiss: (direction) async {
              if (direction == DismissDirection.endToStart) {
                _showCustomizationModal(context, menuItem);
              } else {
                _duplicateItem(context);
              }
              return false;
            },
            child: Card(
              margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 8),
              color: Colors.white24,
              child: Padding(
                padding: const EdgeInsets.all(8.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Expanded(
                          flex: 2,
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              SizedBox(
                                width: MediaQuery.of(context).size.width * 0.2,
                                child: Text(
                                  widget.item.name + _getCustomizationSummary(),
                                  style: GoogleFonts.poppins(
                                    fontSize: 16,
                                    color: Colors.white,
                                    fontWeight: FontWeight.w700,
                                  ),
                                ),
                              ),
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Text(
                                    '\$${_basePrice.toStringAsFixed(2)}',
                                    style: GoogleFonts.poppins(
                                      color: Colors.white,
                                    ),
                                  ),
                                  if (widget.item.customization != null &&
                                      widget.item.customization!.isNotEmpty)
                                    IconButton(
                                      icon: Icon(
                                        _isCustomizationsExpanded
                                            ? Icons.keyboard_arrow_up
                                            : Icons.keyboard_arrow_down,
                                        color: Colors.white,
                                      ),
                                      onPressed: () {
                                        setState(() {
                                          _isCustomizationsExpanded =
                                              !_isCustomizationsExpanded;
                                        });
                                      },
                                    ),
                                ],
                              ),
                            ],
                          ),
                        ),
                        QuantityControls(item: widget.item),
                        Expanded(
                          flex: 1,
                          child: Center(
                            child: Text(
                              '\$${widget.item.total.toStringAsFixed(2)}',
                              style: GoogleFonts.poppins(
                                fontSize: 16,
                                color: Colors.orange,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                    if (customizationDetails.isNotEmpty &&
                        !_isCustomizationsExpanded)
                      Padding(
                        padding: const EdgeInsets.only(
                            left: 16, right: 16, bottom: 8),
                        child: Text(
                          customizationDetails.join(' • '),
                          style: GoogleFonts.poppins(
                            fontSize: 12,
                            color: Colors.grey[400],
                          ),
                        ),
                      ),
                    // Customizations Expansion
                    if (_isCustomizationsExpanded &&
                        widget.item.customization != null &&
                        widget.item.customization!.isNotEmpty)
                      Container(
                        margin: const EdgeInsets.only(top: 12),
                        padding: const EdgeInsets.all(12),
                        decoration: BoxDecoration(
                          color: Colors.white.withOpacity(0.05),
                          borderRadius: BorderRadius.circular(8),
                        ),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // Cooking Option
                            if (widget.item.customization!['cookingOption'] !=
                                null)
                              _buildCustomizationRow(
                                'Cooking:',
                                widget.item.customization!['cookingOption'],
                              ),

                            // Side Dish
                            if (widget.item.customization!['side'] != null)
                              _buildCustomizationRow(
                                'Side:',
                                widget.item.customization!['side']['name'],
                                price: widget.item.customization!['side']
                                    ['price'],
                              ),

                            // Allergies
                            if (widget.item.customization!['allergies'] !=
                                    null &&
                                (widget.item.customization!['allergies']
                                        as List)
                                    .isNotEmpty)
                              _buildCustomizationRow(
                                'Allergies:',
                                (widget.item.customization!['allergies']
                                        as List)
                                    .join(', '),
                              ),

                            // Notes
                            if (widget.item.customization!['notes'] != null &&
                                widget.item.customization!['notes']
                                    .toString()
                                    .isNotEmpty)
                              _buildCustomizationRow(
                                'Notes:',
                                widget.item.customization!['notes'],
                              ),
                          ],
                        ),
                      ),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomizationSection(
      String title, List<String> items, Color chipColor) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          title,
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 6),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: items.map((item) {
            return Chip(
              backgroundColor: Colors.white24,
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(20),
                side: BorderSide(color: Colors.grey.shade300),
              ),
              labelPadding:
                  const EdgeInsets.symmetric(horizontal: 12, vertical: 4),
              label: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  Flexible(
                    child: Text(
                      item,
                      style: GoogleFonts.poppins(
                        fontSize: 14,
                        fontWeight: FontWeight.w500,
                        color: Colors.black87,
                      ),
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                  if (title == 'Side' &&
                      widget.item.customization!['side']?['price'] != null)
                    Padding(
                      padding: const EdgeInsets.only(left: 6),
                      child: Text(
                        '\$${widget.item.customization!['side']['price']}',
                        style: GoogleFonts.poppins(
                          fontSize: 14,
                          fontWeight: FontWeight.w600,
                          color: Colors.black87,
                        ),
                      ),
                    ),
                ],
              ),
            );
          }).toList(),
        ),
      ],
    );
  }

  Widget _specialInstructions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Special Instructions:',
          style: GoogleFonts.poppins(
              fontSize: 14, fontWeight: FontWeight.w600, color: Colors.white),
        ),
        const SizedBox(height: 4),
        Text(
          widget.item.customization!['notes'],
          style: GoogleFonts.poppins(
            fontSize: 14,
            color: Colors.white10,
          ),
        ),
      ],
    );
  }

  Widget _buildCustomizationRow(String label, String value, {double? price}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: Colors.white.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 13,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (price != null && price > 0)
            Text(
              '+\$${price.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 13,
                color: Colors.amber.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }
}
