import 'package:easydine_main/blocs/customization/customization_modal.dart';
import 'package:easydine_main/blocs/pos/pos_bloc.dart';
import 'package:easydine_main/blocs/pos/pos_event.dart';
import 'package:easydine_main/models/menuItem.dart';
import 'package:easydine_main/services/menu_service.dart';
import 'package:easydine_main/widgets/quantity_controls.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:google_fonts/google_fonts.dart';
import '../models/cartItem.dart';
import 'package:sizer/sizer.dart';

class CartItemTile extends StatefulWidget {
  final CartItem item;

  const CartItemTile({
    super.key,
    required this.item,
  });

  @override
  State<CartItemTile> createState() => _CartItemTileState();
}

class _CartItemTileState extends State<CartItemTile> {
  bool _isCustomizationsExpanded = false;

  void _showCustomizationModal(BuildContext context, MenuItem menuItem) {
    showModalBottomSheet(
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.8,
        maxWidth: MediaQuery.of(context).size.width * 0.75,
      ),
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) =>
          ItemCustomizationBottomSheet(item: menuItem, cartItemId: menuItem.id),
    );
  }

  void _duplicateItem(BuildContext context) {
    final customization = widget.item.customization;

    debugPrint('🔄 CartItemTile: Duplicating item ${widget.item.name}');
    debugPrint('🔄 CartItemTile: Cart item ID: ${widget.item.id}');
    debugPrint('🔄 CartItemTile: Base item ID: ${widget.item.baseItemId}');
    debugPrint('🔄 CartItemTile: Customization: $customization');

    if (customization?['type'] == 'miscellaneous') {
      // For miscellaneous items, use the original item ID
      final dishId = widget.item.baseItemId ?? widget.item.id;
      debugPrint('🔄 CartItemTile: Using dish ID for miscellaneous: $dishId');

      context.read<POSBloc>().add(
            AddToCart(
              MenuItem(
                id: dishId,
                name: widget.item.name,
                price: widget.item.price,
                image: '',
                category: 'Miscellaneous',
                description: '',
                ingredients: [],
                prepTime: 0,
                rating: 0,
                isSpicy: false,
                dietaryInfo: [],
              ),
              id: dishId,
              name: widget.item.name,
              price: widget.item.price,
              customization: customization,
            ),
          );
    } else {
      // For regular items, get the dish ID (food ID) not the cart item ID
      final baseItemId = customization?['type'] == 'customized'
          ? customization!['baseItemId']
          : (widget.item.baseItemId ?? widget.item.id);

      debugPrint('🔄 CartItemTile: Using base item ID: $baseItemId');

      // Try to find the menu item in the loaded menu
      final allMenuItems = MenuService.getAllMenuItems();
      debugPrint(
          '🔄 CartItemTile: Available menu items count: ${allMenuItems.length}');

      final menuItem = allMenuItems.firstWhere(
        (item) => item.id == baseItemId,
        orElse: () {
          debugPrint(
              '⚠️ CartItemTile: Menu item not found for ID: $baseItemId');
          debugPrint(
              '⚠️ CartItemTile: Available menu item IDs: ${allMenuItems.map((e) => e.id).take(5).toList()}...');

          // If we can't find the menu item, try to find one with the same name
          final nameMatch = allMenuItems
              .where((item) =>
                  item.name.toLowerCase() == widget.item.name.toLowerCase())
              .firstOrNull;

          if (nameMatch != null) {
            debugPrint(
                '✅ CartItemTile: Found menu item by name match: ${nameMatch.id}');
            return nameMatch;
          }

          debugPrint('❌ CartItemTile: No menu item found, creating fallback');
          return MenuItem(
            id: baseItemId,
            name: widget.item.name,
            price: widget.item.price,
            image: '',
            category: 'Customized',
            description: '',
            ingredients: [],
            prepTime: 0,
            rating: 0,
            isSpicy: false,
            dietaryInfo: [],
          );
        },
      );

      debugPrint('🔄 CartItemTile: Final menu item ID: ${menuItem.id}');

      context.read<POSBloc>().add(
            AddToCart(
              menuItem,
              id: menuItem.id,
              name: menuItem.name,
              price: widget.item.price,
              customization: customization,
            ),
          );
    }
  }

  double get _basePrice {
    // Return only the base price without any side dish price
    return widget.item.price -
        (widget.item.customization?['side']?['price'] ?? 0.0);
  }

  String _getCustomizationSummary() {
    if (widget.item.customization == null ||
        widget.item.customization!.isEmpty) {
      return '';
    }

    final List<String> summary = [];

    if (widget.item.customization!['cookingOption'] != null) {
      summary.add(widget.item.customization!['cookingOption']);
    }

    if (widget.item.customization!['side'] != null) {
      summary.add(widget.item.customization!['side']['name']);
    }

    return summary.isNotEmpty ? ' (${summary.join(', ')})' : '';
  }

  @override
  Widget build(BuildContext context) {
    final isCustomized = widget.item.customization?['type'] == 'customized';
    final isMiscellaneous =
        widget.item.customization?['type'] == 'miscellaneous';

    // Create a default MenuItem for miscellaneous or customized items
    final defaultMenuItem = MenuItem(
      id: widget.item.id,
      name: widget.item.name,
      price: widget.item.price,
      image: '',
      category: isCustomized ? 'Customized' : 'Miscellaneous',
      description: '',
      ingredients: [],
      prepTime: 0,
      rating: 0,
      isSpicy: false,
      dietaryInfo: [],
    );

    // Try to find the menu item, use default if not found
    final MenuItem menuItem = isMiscellaneous
        ? defaultMenuItem
        : MenuService.getAllMenuItems().firstWhere(
            (item) =>
                item.id ==
                (isCustomized
                    ? widget.item.customization != null
                        ? widget.item.customization!['baseItemId']
                        : null
                    : widget.item.id),
            orElse: () => defaultMenuItem,
          );

    // Build customization summary
    final List<String> customizationDetails = [];
    if (isCustomized) {
      final customization = widget.item.customization!;

      if (customization['cookingOption'] != null) {
        customizationDetails.add(customization['cookingOption']);
      }

      if (customization['side'] != null) {
        customizationDetails.add('Side: ${customization['side']['name']}');
      }

      if (customization['allergies'] != null &&
          (customization['allergies'] as List).isNotEmpty) {
        customizationDetails
            .add('Allergies: ${customization['allergies'].join(', ')}');
      }

      // Add addons to summary
      if (customization['dishAddons'] != null &&
          (customization['dishAddons'] as List).isNotEmpty) {
        final addons = customization['dishAddons'] as List;
        final totalAddons = addons.fold<int>(0,
            (sum, addon) => sum + ((addon['quantity'] as num?)?.toInt() ?? 1));
        customizationDetails.add('$totalAddons addon(s)');
      }

      // Add extras to summary
      if (customization['dishExtras'] != null &&
          (customization['dishExtras'] as List).isNotEmpty) {
        final extras = customization['dishExtras'] as List;
        final totalExtras = extras.fold<int>(0,
            (sum, extra) => sum + ((extra['quantity'] as num?)?.toInt() ?? 1));
        customizationDetails.add('$totalExtras extra(s)');
      }

      // Add sides to summary
      if (customization['dishSides'] != null &&
          (customization['dishSides'] as List).isNotEmpty) {
        final sides = customization['dishSides'] as List;
        final totalSides = sides.fold<int>(
            0, (sum, side) => sum + ((side['quantity'] as num?)?.toInt() ?? 1));
        customizationDetails.add('$totalSides side(s)');
      }

      // Add beverages to summary
      if (customization['dishBeverages'] != null &&
          (customization['dishBeverages'] as List).isNotEmpty) {
        final beverages = customization['dishBeverages'] as List;
        final totalBeverages = beverages.fold<int>(
            0,
            (sum, beverage) =>
                sum + ((beverage['quantity'] as num?)?.toInt() ?? 1));
        customizationDetails.add('$totalBeverages beverage(s)');
      }

      // Add desserts to summary
      if (customization['dishDesserts'] != null &&
          (customization['dishDesserts'] as List).isNotEmpty) {
        final desserts = customization['dishDesserts'] as List;
        final totalDesserts = desserts.fold<int>(
            0,
            (sum, dessert) =>
                sum + ((dessert['quantity'] as num?)?.toInt() ?? 1));
        customizationDetails.add('$totalDesserts dessert(s)');
      }

      if (customization['notes']?.isNotEmpty ?? false) {
        customizationDetails.add('Notes: ${customization['notes']}');
      }
    }

    return LayoutBuilder(
      builder: (context, constraints) {
        // Calculate dynamic height based on content and screen size
        double baseHeight = 7.h; // Increased base height
        double expandedHeight = _isCustomizationsExpanded ? 15.h : baseHeight;
        bool hasCustomizations = customizationDetails.isNotEmpty;

        // Adjust height based on content
        if (hasCustomizations && !_isCustomizationsExpanded) {
          baseHeight += 1.5.h; // Add space for customization summary
        }

        final finalHeight =
            _isCustomizationsExpanded ? expandedHeight : baseHeight;

        return ClipRRect(
          child: Dismissible(
            key: Key(widget.item.id),
            background: Container(
              color: Colors.green.withOpacity(0.7),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 0.1.h),
                    child: Icon(Icons.copy, color: Colors.white),
                  ),
                  Text(
                    'Duplicate',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            secondaryBackground: Container(
              color: Colors.red.withOpacity(0.7),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.end,
                children: [
                  Padding(
                    padding: EdgeInsets.symmetric(horizontal: 0.1.h),
                    child: Icon(Icons.edit, color: Colors.white),
                  ),
                  Text(
                    'Edit Customization',
                    style: GoogleFonts.poppins(
                      color: Colors.white,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            movementDuration: const Duration(milliseconds: 200),
            dismissThresholds: const {
              DismissDirection.startToEnd: 0.3,
              DismissDirection.endToStart: 0.3,
            },
            confirmDismiss: (direction) async {
              if (direction == DismissDirection.endToStart) {
                debugPrint('Edit customization for item: ${menuItem.id}');
                _showCustomizationModal(context, menuItem);
              } else {
                _duplicateItem(context);
              }
              return false;
            },
            child: Card(
              margin: EdgeInsets.symmetric(horizontal: 0.4.h, vertical: 0.1.w),
              color: Colors.white24,
              child: Padding(
                padding:
                    EdgeInsets.symmetric(vertical: 0.2.h, horizontal: 0.5.h),
                child: IntrinsicHeight(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Row(
                        crossAxisAlignment: CrossAxisAlignment.center,
                        children: [
                          Expanded(
                            flex: 3,
                            child: Column(
                              crossAxisAlignment: CrossAxisAlignment.start,
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Flexible(
                                  child: Text(
                                    widget.item.name +
                                        _getCustomizationSummary(),
                                    style: GoogleFonts.poppins(
                                      fontSize: 10.sp,
                                      color: Colors.white,
                                      fontWeight: FontWeight.w700,
                                    ),
                                    maxLines: 2,
                                    overflow: TextOverflow.ellipsis,
                                  ),
                                ),
                                SizedBox(height: 0.5.h),
                                Row(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Text(
                                      '\$${_basePrice.toStringAsFixed(2)}',
                                      style: GoogleFonts.poppins(
                                        color: Colors.white,
                                        fontSize: 10.sp,
                                      ),
                                    ),
                                    if (widget.item.customization != null &&
                                        widget.item.customization!.isNotEmpty)
                                      SizedBox(
                                        width: 4.w,
                                        height: 3.h,
                                        child: IconButton(
                                          icon: Icon(
                                            _isCustomizationsExpanded
                                                ? Icons.keyboard_arrow_up
                                                : Icons.keyboard_arrow_down,
                                            color: Colors.white,
                                            size: 15.sp,
                                          ),
                                          onPressed: () {
                                            setState(() {
                                              _isCustomizationsExpanded =
                                                  !_isCustomizationsExpanded;
                                            });
                                          },
                                          padding: EdgeInsets.zero,
                                        ),
                                      ),
                                  ],
                                ),
                              ],
                            ),
                          ),
                          SizedBox(width: 0.5.w),
                          QuantityControls(item: widget.item),
                          SizedBox(width: 0.5.w),
                          Expanded(
                            flex: 2,
                            child: Center(
                              child: Text(
                                '\$${widget.item.total.toStringAsFixed(2)}',
                                style: GoogleFonts.poppins(
                                  fontSize: 10.sp,
                                  color: Colors.orange,
                                  fontWeight: FontWeight.bold,
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (customizationDetails.isNotEmpty &&
                          !_isCustomizationsExpanded)
                        Padding(
                          padding: EdgeInsets.only(
                              left: 1.w, right: 1.w, bottom: 0.5.h, top: 0.2.h),
                          child: Text(
                            customizationDetails.join(' • '),
                            style: GoogleFonts.poppins(
                              fontSize: 10.sp,
                              color: Colors.grey[400],
                            ),
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                          ),
                        ),
                      // Customizations Expansion
                      if (_isCustomizationsExpanded &&
                          customizationDetails.isNotEmpty)
                        Container(
                          margin: const EdgeInsets.only(top: 12),
                          padding: const EdgeInsets.all(12),
                          decoration: BoxDecoration(
                            color: Colors.white.withOpacity(0.05),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Cooking Option
                              if (widget.item.customization!['cookingOption'] !=
                                  null)
                                _buildCustomizationRow(
                                  'Cooking:',
                                  widget.item.customization!['cookingOption'],
                                ),

                              // Side Dish
                              if (widget.item.customization!['side'] != null)
                                _buildCustomizationRow(
                                  'Side:',
                                  widget.item.customization!['side']['name'],
                                  price: widget.item.customization!['side']
                                      ['price'],
                                ),

                              // Allergies
                              if (widget.item.customization!['allergies'] !=
                                      null &&
                                  (widget.item.customization!['allergies']
                                          as List)
                                      .isNotEmpty)
                                _buildCustomizationRow(
                                  'Allergies:',
                                  (widget.item.customization!['allergies']
                                          as List)
                                      .join(', '),
                                ),

                              // Dish Addons
                              if (widget.item.customization!['dishAddons'] !=
                                      null &&
                                  (widget.item.customization!['dishAddons']
                                          as List)
                                      .isNotEmpty)
                                _buildCustomizationListRow(
                                  'Addons:',
                                  widget.item.customization!['dishAddons']
                                      as List,
                                ),

                              // Dish Extras
                              if (widget.item.customization!['dishExtras'] !=
                                      null &&
                                  (widget.item.customization!['dishExtras']
                                          as List)
                                      .isNotEmpty)
                                _buildCustomizationListRow(
                                  'Extras:',
                                  widget.item.customization!['dishExtras']
                                      as List,
                                ),

                              // Dish Sides
                              if (widget.item.customization!['dishSides'] !=
                                      null &&
                                  (widget.item.customization!['dishSides']
                                          as List)
                                      .isNotEmpty)
                                _buildCustomizationListRow(
                                  'Sides:',
                                  widget.item.customization!['dishSides']
                                      as List,
                                ),

                              // Dish Beverages
                              if (widget.item.customization!['dishBeverages'] !=
                                      null &&
                                  (widget.item.customization!['dishBeverages']
                                          as List)
                                      .isNotEmpty)
                                _buildCustomizationListRow(
                                  'Beverages:',
                                  widget.item.customization!['dishBeverages']
                                      as List,
                                ),

                              // Dish Desserts
                              if (widget.item.customization!['dishDesserts'] !=
                                      null &&
                                  (widget.item.customization!['dishDesserts']
                                          as List)
                                      .isNotEmpty)
                                _buildCustomizationListRow(
                                  'Desserts:',
                                  widget.item.customization!['dishDesserts']
                                      as List,
                                ),

                              // Notes
                              if (widget.item.customization!['notes'] != null &&
                                  widget.item.customization!['notes']
                                      .toString()
                                      .isNotEmpty)
                                _buildCustomizationRow(
                                  'Notes:',
                                  widget.item.customization!['notes'],
                                ),
                            ],
                          ),
                        ),
                    ],
                  ),
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  Widget _buildCustomizationRow(String label, String value, {double? price}) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: Colors.white.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              value,
              style: GoogleFonts.poppins(
                fontSize: 13,
                color: Colors.white,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          if (price != null && price > 0)
            Text(
              '+\$${price.toStringAsFixed(2)}',
              style: GoogleFonts.poppins(
                fontSize: 13,
                color: Colors.amber.shade600,
                fontWeight: FontWeight.w500,
              ),
            ),
        ],
      ),
    );
  }

  Widget _buildCustomizationListRow(String label, List<dynamic> items) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            label,
            style: GoogleFonts.poppins(
              fontSize: 13,
              color: Colors.white.withOpacity(0.6),
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          ...items.map((item) {
            final name = item['name'] ?? 'Unknown';
            final quantity = item['quantity'] ?? 1;
            final price = item['price']?.toDouble();

            return Padding(
              padding: const EdgeInsets.only(left: 16, bottom: 2),
              child: Row(
                children: [
                  Expanded(
                    child: Text(
                      '$name (${quantity}x)',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.white,
                        fontWeight: FontWeight.w400,
                      ),
                    ),
                  ),
                  if (price != null && price > 0)
                    Text(
                      '+\$${(price * quantity).toStringAsFixed(2)}',
                      style: GoogleFonts.poppins(
                        fontSize: 12,
                        color: Colors.amber.shade600,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                ],
              ),
            );
          }),
        ],
      ),
    );
  }
}
